import React, { useState } from 'react';
import { CheckCircle, Star, Target, Settings, ChevronDown, ChevronUp, Package, Award } from 'lucide-react';

interface ProductDescriptionProps {
  description: string;
  productName?: string;
  productPrice?: number;
  productBrand?: string;
}

interface ParsedSection {
  title: string;
  items: string[];
  type: 'list' | 'specifications' | 'features';
}

interface ParsedDescription {
  overview: string;
  sections: ParsedSection[];
}

interface SpecificationItem {
  key: string;
  value: string;
}

const ProductDescription: React.FC<ProductDescriptionProps> = ({
  description,
  productName = '',
  productPrice,
  productBrand
}) => {
  const [expandedSections, setExpandedSections] = useState<Set<number>>(new Set([0, 1])); // Expand first two sections by default

  const toggleSection = (index: number) => {
    const newExpanded = new Set(expandedSections);
    if (newExpanded.has(index)) {
      newExpanded.delete(index);
    } else {
      newExpanded.add(index);
    }
    setExpandedSections(newExpanded);
  };

  const parseSpecifications = (items: string[]): SpecificationItem[] => {
    const specs: SpecificationItem[] = [];

    items.forEach(item => {
      // Try to parse key-value pairs (e.g., "Power: 3 W", "Model: HW80")
      const colonIndex = item.indexOf(':');
      if (colonIndex > 0 && colonIndex < item.length - 1) {
        specs.push({
          key: item.substring(0, colonIndex).trim(),
          value: item.substring(colonIndex + 1).trim()
        });
        return;
      }

      // Try to extract power specifications (e.g., "3 W", "1500 W")
      const powerMatch = item.match(/(\d+(?:\.\d+)?)\s*W/i);
      if (powerMatch) {
        specs.push({
          key: 'Power Consumption',
          value: powerMatch[0]
        });
        return;
      }

      // Try to extract model numbers (e.g., "Model HW80", "HW80")
      const modelMatch = item.match(/(?:Model\s+)?([A-Z]+\d+[A-Z]*\d*)/i);
      if (modelMatch) {
        specs.push({
          key: 'Model Number',
          value: modelMatch[1]
        });
        return;
      }

      // Try to extract capacity (e.g., "8 kg", "10 L")
      const capacityMatch = item.match(/(\d+(?:\.\d+)?)\s*(kg|l|liters?|litres?)/i);
      if (capacityMatch) {
        specs.push({
          key: 'Capacity',
          value: `${capacityMatch[1]} ${capacityMatch[2].toLowerCase()}`
        });
        return;
      }

      // Try to extract RPM (e.g., "1400 RPM")
      const rpmMatch = item.match(/(\d+)\s*rpm/i);
      if (rpmMatch) {
        specs.push({
          key: 'Spin Speed',
          value: `${rpmMatch[1]} RPM`
        });
        return;
      }

      // Try to extract dimensions
      const dimensionMatch = item.match(/(\d+(?:\.\d+)?)\s*x\s*(\d+(?:\.\d+)?)\s*x\s*(\d+(?:\.\d+)?)\s*(cm|mm|m)/i);
      if (dimensionMatch) {
        specs.push({
          key: 'Dimensions',
          value: `${dimensionMatch[1]} x ${dimensionMatch[2]} x ${dimensionMatch[3]} ${dimensionMatch[4]}`
        });
        return;
      }

      // Try to extract weight
      const weightMatch = item.match(/(\d+(?:\.\d+)?)\s*(kg|g)/i);
      if (weightMatch && !item.toLowerCase().includes('capacity')) {
        specs.push({
          key: 'Weight',
          value: `${weightMatch[1]} ${weightMatch[2]}`
        });
        return;
      }

      // If no specific pattern matches, treat as a general specification
      if (item.trim()) {
        specs.push({
          key: 'Feature',
          value: item.trim()
        });
      }
    });

    return specs;
  };

  const isGenericContent = (text: string): boolean => {
    const genericPhrases = [
      'high quality',
      'reliable performance',
      'durable construction',
      'professional-grade performance and reliability',
      'designed for optimal performance',
      'excellent features and performance',
      'quality product designed for reliable performance',
      'high-quality construction ensures durability',
      'reliable performance for consistent results',
      'user-friendly design for easy operation',
      'premium quality features',
      'excellent value for money',
      'designed for long-lasting performance'
    ];

    const lowerText = text.toLowerCase();
    return genericPhrases.some(phrase => lowerText.includes(phrase));
  };

  const filterGenericItems = (items: string[]): string[] => {
    return items.filter(item => !isGenericContent(item));
  };

  const extractSpecsFromProductName = (productName: string): SpecificationItem[] => {
    const specs: SpecificationItem[] = [];
    const name = productName.toLowerCase();

    // Extract model number
    const modelMatch = productName.match(/([A-Z]+\d+[-\w]*)/);
    if (modelMatch) {
      specs.push({ key: 'Model', value: modelMatch[1] });
    }

    // Extract dimensions
    const dimensionMatch = productName.match(/(\d+(?:\.\d+)?)\s*(?:cm|mm|inch)/i);
    if (dimensionMatch) {
      specs.push({ key: 'Size', value: dimensionMatch[0] });
    }

    // Extract capacity for washing machines
    if (name.includes('washing') || name.includes('washer')) {
      const capacityMatch = productName.match(/(\d+(?:\.\d+)?)\s*kg/i);
      if (capacityMatch) {
        specs.push({ key: 'Capacity', value: capacityMatch[0] });
      }
    }

    // Extract power ratings
    const powerMatch = productName.match(/(\d+)\s*w/i);
    if (powerMatch) {
      specs.push({ key: 'Power', value: `${powerMatch[1]} W` });
    }

    return specs;
  };

  const parseDescription = (desc: string): ParsedDescription | null => {
    if (!desc || !desc.trim()) return null;

    // Clean up the description and handle the actual format we receive
    const cleanDesc = desc.trim();

    // Extract overview (everything before the first section)
    const overviewMatch = cleanDesc.match(/^(.*?)(?=\s*(?:Key Features|Features|Technical Specifications|Specifications|Benefits|Ideal For):\s*•)/);
    let overview = overviewMatch ? overviewMatch[1].trim() : '';

    // Filter out generic overview content
    if (isGenericContent(overview)) {
      overview = ''; // Hide generic overviews
    }

    // Find all sections using regex that matches our actual format
    const sectionRegex = /(Key Features|Features|Technical Specifications|Specifications|Benefits|Ideal For):\s*((?:•[^•]*)*)/g;
    const sections: ParsedSection[] = [];

    let match;
    while ((match = sectionRegex.exec(cleanDesc)) !== null) {
      const title = match[1].trim();
      const content = match[2].trim();

      // Extract bullet points
      const bulletRegex = /•\s*([^•]+)/g;
      const items: string[] = [];
      let bulletMatch;

      while ((bulletMatch = bulletRegex.exec(content)) !== null) {
        const item = bulletMatch[1].trim();
        if (item) {
          items.push(item);
        }
      }

      if (items.length > 0) {
        // Filter out generic items
        const filteredItems = filterGenericItems(items);

        // Only include section if it has non-generic content
        if (filteredItems.length > 0) {
          // Determine section type
          let sectionType: 'list' | 'specifications' | 'features' = 'list';

          if (title.toLowerCase().includes('specification')) {
            sectionType = 'specifications';
          } else if (title.toLowerCase().includes('feature')) {
            sectionType = 'features';
          }

          sections.push({
            title,
            items: filteredItems,
            type: sectionType
          });
        }
      }
    }

    return { overview, sections };
  };

  const getSectionIcon = (title: string) => {
    const iconClass = "w-5 h-5";

    switch (title.toLowerCase()) {
      case 'key features':
      case 'features':
        return <Star className={`${iconClass} text-amber-600`} />;
      case 'technical specifications':
      case 'specifications':
        return <Settings className={`${iconClass} text-blue-600`} />;
      case 'benefits':
        return <Award className={`${iconClass} text-green-600`} />;
      case 'ideal for':
        return <Target className={`${iconClass} text-purple-600`} />;
      default:
        return <Package className={`${iconClass} text-gray-600`} />;
    }
  };

  const renderSpecificationTable = (items: string[]) => {
    const specs = parseSpecifications(items);

    if (specs.length === 0) {
      return (
        <div className="text-gray-500 italic text-center py-4">
          No specifications available
        </div>
      );
    }

    return (
      <div className="bg-white rounded-lg overflow-hidden border border-gray-200 shadow-sm">
        {/* Table Header */}
        <div className="bg-gradient-to-r from-blue-600 to-blue-700 px-3 sm:px-4 lg:px-6 py-2 sm:py-3">
          <h4 className="text-white font-semibold text-sm sm:text-base">Technical Specifications</h4>
        </div>

        {/* Specifications Table */}
        <div className="divide-y divide-gray-200">
          {specs.map((spec, index) => (
            <div key={index} className="flex flex-col sm:flex-row hover:bg-blue-50 transition-colors duration-200">
              <div className="sm:w-2/5 px-3 sm:px-4 lg:px-6 py-2 sm:py-3 lg:py-4 bg-gray-50 sm:border-r border-gray-200">
                <span className="text-xs sm:text-sm font-semibold text-gray-800 block">{spec.key}</span>
              </div>
              <div className="sm:w-3/5 px-3 sm:px-4 lg:px-6 py-2 sm:py-3 lg:py-4 bg-white">
                <span className="text-xs sm:text-sm text-gray-900 font-medium block">
                  {spec.value || '—'}
                </span>
              </div>
            </div>
          ))}
        </div>

        {/* Footer */}
        <div className="bg-gray-50 px-3 sm:px-4 lg:px-6 py-1 sm:py-2 border-t border-gray-200">
          <p className="text-xs text-gray-500 text-center">
            Specifications may vary by model and region
          </p>
        </div>
      </div>
    );
  };

  const renderFeatureGrid = (items: string[]) => {
    return (
      <div className="space-y-3 sm:space-y-4">
        {/* Features Header */}
        <div className="bg-gradient-to-r from-amber-500 to-orange-500 px-3 sm:px-4 py-2 rounded-lg">
          <h4 className="text-white font-semibold text-xs sm:text-sm">Key Features & Highlights</h4>
        </div>

        {/* Features Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-2 sm:gap-3 lg:gap-4">
          {items.map((item, index) => (
            <div key={index} className="flex items-start p-3 sm:p-4 bg-gradient-to-r from-amber-50 to-orange-50 rounded-lg border border-amber-200 hover:from-amber-100 hover:to-orange-100 transition-all duration-200 shadow-sm">
              <div className="w-2 h-2 sm:w-3 sm:h-3 bg-gradient-to-r from-amber-500 to-orange-500 rounded-full mt-1 sm:mt-1.5 mr-2 sm:mr-3 flex-shrink-0"></div>
              <span className="text-xs sm:text-sm text-gray-800 font-medium leading-relaxed">{item}</span>
            </div>
          ))}
        </div>
      </div>
    );
  };

  const renderBenefitsList = (items: string[]) => {
    return (
      <div className="space-y-3 sm:space-y-4">
        {/* Benefits Header */}
        <div className="bg-gradient-to-r from-green-600 to-emerald-600 px-3 sm:px-4 py-2 rounded-lg">
          <h4 className="text-white font-semibold text-xs sm:text-sm">Benefits & Advantages</h4>
        </div>

        {/* Benefits List */}
        <div className="space-y-2 sm:space-y-3">
          {items.map((item, index) => (
            <div key={index} className="flex items-start p-3 sm:p-4 bg-gradient-to-r from-green-50 to-emerald-50 rounded-lg border border-green-200 hover:from-green-100 hover:to-emerald-100 transition-all duration-200 shadow-sm">
              <CheckCircle className="w-4 h-4 sm:w-5 sm:h-5 text-green-600 mt-0.5 mr-2 sm:mr-3 flex-shrink-0" />
              <span className="text-xs sm:text-sm text-gray-800 font-medium leading-relaxed">{item}</span>
            </div>
          ))}
        </div>
      </div>
    );
  };

  const renderDefaultList = (items: string[]) => {
    return (
      <div className="space-y-2">
        {items.map((item, index) => (
          <div key={index} className="flex items-start p-2 hover:bg-gray-50 rounded-md transition-colors">
            <div className="w-2 h-2 bg-gray-400 rounded-full mt-2 mr-3 flex-shrink-0"></div>
            <span className="text-sm text-gray-700">{item}</span>
          </div>
        ))}
      </div>
    );
  };

  const generateFallbackContent = () => {
    const specs = extractSpecsFromProductName(productName);

    // Add price if available
    if (productPrice) {
      specs.push({ key: 'Price', value: `₹${productPrice.toLocaleString()}` });
    }

    // Add brand if available
    if (productBrand) {
      specs.push({ key: 'Brand', value: productBrand });
    }

    // Generate dynamic overview based on product name and brand
    const generateDynamicOverview = () => {
      const name = productName.toLowerCase();
      let overview = '';

      if (productBrand) {
        overview = `The ${productBrand} ${productName}`;
      } else {
        overview = `The ${productName}`;
      }

      // Add specific descriptions based on product type
      if (name.includes('lamp') || name.includes('light')) {
        overview += ' provides efficient lighting for your space.';
      } else if (name.includes('camera') || name.includes('dashcam')) {
        overview += ' offers reliable recording and monitoring capabilities.';
      } else if (name.includes('pump')) {
        overview += ' delivers dependable inflation performance.';
      } else if (name.includes('hood') || name.includes('chimney')) {
        overview += ' ensures effective kitchen ventilation.';
      } else if (name.includes('hinge') || name.includes('hardware')) {
        overview += ' provides smooth and reliable operation.';
      } else {
        overview += ' is designed to meet your specific requirements.';
      }

      return overview;
    };

    return {
      overview: generateDynamicOverview(),
      sections: specs.length > 0 ? [{
        title: 'Specifications',
        items: specs.map(spec => `${spec.key}: ${spec.value}`),
        type: 'specifications' as const
      }] : []
    };
  };

  const parsedDesc = parseDescription(description);

  // If no meaningful content found, generate fallback
  const finalDesc = parsedDesc && parsedDesc.sections.length > 0
    ? parsedDesc
    : generateFallbackContent();

  if (!finalDesc || (finalDesc.sections.length === 0 && !finalDesc.overview)) {
    return (
      <div className="prose prose-sm max-w-none">
        <div className="bg-gray-50 rounded-lg p-4 border border-gray-200">
          <p className="text-gray-700 leading-relaxed">
            {description || `${productName} - Product information will be updated soon.`}
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="product-description space-y-4 sm:space-y-6">
      {/* Overview Section */}
      {finalDesc.overview && (
        <div className="overview-section bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg p-4 sm:p-6 border border-blue-100">
          <div className="flex items-start">
            <Package className="w-5 h-5 sm:w-6 sm:h-6 text-blue-600 mt-1 mr-2 sm:mr-3 flex-shrink-0" />
            <div>
              <h3 className="text-base sm:text-lg font-semibold text-gray-900 mb-2">Product Overview</h3>
              <p className="text-gray-700 leading-relaxed text-sm sm:text-base">
                {finalDesc.overview}
              </p>
            </div>
          </div>
        </div>
      )}

      {/* Dynamic Sections */}
      {finalDesc.sections.map((section, index) => (
        <div key={index} className="description-section bg-white rounded-lg border border-gray-200 overflow-hidden shadow-sm">
          {/* Section Header */}
          <button
            onClick={() => toggleSection(index)}
            className="w-full px-4 sm:px-6 py-3 sm:py-4 bg-gray-50 hover:bg-gray-100 transition-colors flex items-center justify-between border-b border-gray-200"
          >
            <div className="flex items-center min-w-0 flex-1">
              {getSectionIcon(section.title)}
              <span className="ml-2 sm:ml-3 text-sm sm:text-lg font-semibold text-gray-900 truncate">{section.title}</span>
              <span className="ml-1 sm:ml-2 text-xs sm:text-sm text-gray-500 flex-shrink-0">({section.items.length})</span>
            </div>
            {expandedSections.has(index) ? (
              <ChevronUp className="w-4 h-4 sm:w-5 sm:h-5 text-gray-500 flex-shrink-0 ml-2" />
            ) : (
              <ChevronDown className="w-4 h-4 sm:w-5 sm:h-5 text-gray-500 flex-shrink-0 ml-2" />
            )}
          </button>

          {/* Section Content */}
          {expandedSections.has(index) && (
            <div className="p-4 sm:p-6">
              {section.type === 'specifications' && renderSpecificationTable(section.items)}
              {section.type === 'features' && renderFeatureGrid(section.items)}
              {section.title.toLowerCase().includes('benefit') && renderBenefitsList(section.items)}
              {section.type === 'list' && !section.title.toLowerCase().includes('benefit') && renderDefaultList(section.items)}
            </div>
          )}
        </div>
      ))}
    </div>
  );
};

export default ProductDescription;
