#!/usr/bin/env python3
"""
Enhanced Product Description Generator Script

This script generates comprehensive, structured product descriptions optimized
for the new ProductDescription frontend component. It focuses on products that
need better descriptions and creates formatted content with sections like
Key Features, Technical Specifications, Benefits, and Ideal For.

Features:
- Targets products with missing or inadequate descriptions
- Generates structured descriptions compatible with frontend component
- Uses intelligent analysis and web scraping for relevant content
- Creates properly formatted sections (Features, Specifications, Benefits, etc.)
- Comprehensive error handling and logging
- Database connection management using .env configuration

Author: Triumph Enterprise
Created: 2025
Updated: 2025 - Enhanced for new frontend component
"""

import os
import sys
import django
import json
import requests
import time
import logging
from datetime import datetime
from typing import Dict, List, Optional, Any
from urllib.parse import quote_plus
import re
from dataclasses import dataclass, asdict
try:
    from bs4 import BeautifulSoup
    HAS_BEAUTIFULSOUP = True
except ImportError:
    HAS_BEAUTIFULSOUP = False
    print("Warning: BeautifulSoup not available. Install with: pip install beautifulsoup4")

# Setup Django environment
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "backend.settings")
django.setup()

from django.db import connection
from products.models import Product, Category, Brand, ProductImage, SubCategorie, GST
from django.core.serializers.json import DjangoJSONEncoder

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('enhanced_description_generation.log'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

@dataclass
class ProductData:
    """Data class for enhanced product information"""
    id: int
    name: str
    slug: Optional[str]
    original_description: Optional[str]
    enhanced_description: Optional[str]
    category: Optional[str]
    subcategory: Optional[str]
    brand: Optional[str]
    price: float
    mrp: float
    base_price: float
    gst_rate: float
    gst_amount: float
    stock: int
    is_active: bool
    created_at: str
    updated_at: str
    images: List[str]
    description_quality_score: int  # 1-10 rating of description quality
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for JSON serialization"""
        return asdict(self)

class StructuredDescriptionGenerator:
    """Generates structured descriptions optimized for the frontend component"""
    
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        })
        self.delay_between_requests = 2  # seconds
        
    def generate_structured_description(self, product_name: str, brand: str = None,
                                      category: str = None, existing_description: str = None) -> str:
        """Generate structured description with proper sections for frontend component"""
        try:
            logger.info(f"Generating structured description for: {product_name}")
            
            # Generate overview section
            overview = self.generate_product_overview(product_name, brand, category, existing_description)
            
            # Generate key features
            features = self.generate_key_features(product_name, brand, category)
            
            # Generate technical specifications
            specifications = self.generate_technical_specifications(product_name, brand, category)
            
            # Generate benefits
            benefits = self.generate_product_benefits(product_name, brand, category)
            
            # Generate ideal for section
            ideal_for = self.generate_ideal_for_section(product_name, category)
            
            # Compile structured description
            structured_desc = self.compile_structured_description(
                overview, features, specifications, benefits, ideal_for
            )
            
            logger.info(f"Generated structured description ({len(structured_desc)} chars)")
            return structured_desc
            
        except Exception as e:
            logger.error(f"Error generating structured description for {product_name}: {str(e)}")
            return self.generate_fallback_description(product_name, brand, category)
    
    def generate_product_overview(self, product_name: str, brand: str = None, 
                                category: str = None, existing_description: str = None) -> str:
        """Generate comprehensive product overview"""
        try:
            # Use existing description if it's substantial and meaningful
            if existing_description and len(existing_description.strip()) > 50:
                cleaned_existing = self.clean_description_text(existing_description)
                if not self.is_generic_content(cleaned_existing):
                    return cleaned_existing
            
            # Generate intelligent overview based on product analysis
            name_lower = product_name.lower()
            overview_parts = []
            
            if brand:
                overview_parts.append(f"The {brand} {product_name}")
            else:
                overview_parts.append(f"The {product_name}")
            
            # Add specific descriptions based on product type detection
            if any(word in name_lower for word in ['lamp', 'light', 'led']):
                overview_parts.append("is a modern lighting solution designed to provide optimal illumination.")
                if 'study' in name_lower:
                    overview_parts.append("Specifically engineered for study and work environments with adjustable brightness.")
                    
            elif any(word in name_lower for word in ['hinge', 'clip', 'soft close']):
                overview_parts.append("is a precision-engineered hardware component for smooth operation.")
                if 'soft close' in name_lower:
                    overview_parts.append("Features advanced soft-close technology for quiet, gentle closing.")
                    
            elif any(word in name_lower for word in ['camera', 'dashcam', 'dash cam']):
                overview_parts.append("is an advanced recording device for high-quality video capture.")
                if 'dashcam' in name_lower:
                    overview_parts.append("Perfect for vehicle security and incident recording.")
                    
            elif any(word in name_lower for word in ['door bell', 'doorbell', 'wifi']):
                overview_parts.append("is a smart home security device enhancing safety and convenience.")
                if 'wifi' in name_lower:
                    overview_parts.append("Features wireless connectivity for remote monitoring.")
                    
            elif any(word in name_lower for word in ['pump', 'air pump', 'tyre']):
                overview_parts.append("is a reliable inflation device for automotive and general use.")
                
            elif any(word in name_lower for word in ['hood', 'kitchen', 'chimney']):
                overview_parts.append("is a premium kitchen appliance for maintaining a clean cooking environment.")
                
            else:
                if category:
                    overview_parts.append(f"is a high-quality {category.lower()} designed for professional performance.")
                else:
                    overview_parts.append("is a premium product engineered for exceptional performance and durability.")
            
            return " ".join(overview_parts)
            
        except Exception as e:
            logger.error(f"Error generating overview: {str(e)}")
            return f"High-quality {product_name} designed for optimal performance and reliability."

    def generate_key_features(self, product_name: str, brand: str = None, category: str = None) -> List[str]:
        """Generate key features list for the product"""
        try:
            name_lower = product_name.lower()
            features = []

            # Product-specific features based on name analysis
            if any(word in name_lower for word in ['lamp', 'light', 'led']):
                features.extend([
                    "Energy-efficient LED technology for long-lasting performance",
                    "Adjustable brightness settings for optimal illumination",
                    "Eye-friendly lighting reduces strain during extended use",
                    "Durable construction with premium materials"
                ])
                if 'study' in name_lower:
                    features.extend([
                        "Focused task lighting for enhanced productivity",
                        "Flexible positioning for customized lighting angles"
                    ])

            elif any(word in name_lower for word in ['hinge', 'clip', 'soft close']):
                features.extend([
                    "Precision-engineered mechanism for smooth operation",
                    "Durable metal construction ensures long-lasting performance",
                    "Easy installation with included mounting hardware",
                    "Corrosion-resistant finish for extended durability"
                ])
                if 'soft close' in name_lower:
                    features.extend([
                        "Advanced soft-close technology prevents slamming",
                        "Whisper-quiet operation for peaceful environment"
                    ])

            elif any(word in name_lower for word in ['camera', 'dashcam', 'dash cam']):
                features.extend([
                    "High-definition video recording for crystal-clear footage",
                    "Wide-angle lens captures comprehensive field of view",
                    "Night vision capability for 24/7 monitoring",
                    "Loop recording automatically overwrites old files"
                ])
                if '3k' in name_lower or '1296p' in name_lower:
                    features.append("Ultra-high 3K/1296p resolution for exceptional detail")
                if 'dashcam' in name_lower:
                    features.extend([
                        "G-sensor detects impacts and locks important footage",
                        "GPS tracking records location and speed data"
                    ])

            elif any(word in name_lower for word in ['door bell', 'doorbell', 'wifi']):
                features.extend([
                    "Smart home integration with popular platforms",
                    "Mobile app connectivity for remote access",
                    "Real-time push notifications to your smartphone",
                    "Two-way audio communication with visitors"
                ])
                if 'wifi' in name_lower:
                    features.extend([
                        "Wireless WiFi connectivity eliminates wiring needs",
                        "Cloud storage support for recorded footage"
                    ])

            elif any(word in name_lower for word in ['pump', 'air pump', 'tyre']):
                features.extend([
                    "High-pressure capability for various inflation needs",
                    "Digital pressure gauge for accurate readings",
                    "Auto shut-off function prevents over-inflation",
                    "Portable design with convenient carrying handle"
                ])
                if '150 psi' in name_lower:
                    features.append("Powerful 150 PSI maximum pressure capacity")
                if 'car' in name_lower and 'bike' in name_lower:
                    features.append("Universal compatibility for cars, bikes, and more")

            elif any(word in name_lower for word in ['hood', 'kitchen', 'chimney']):
                features.extend([
                    "Powerful suction motor effectively removes smoke and odors",
                    "Multiple speed settings for optimal performance",
                    "Energy-efficient LED lighting illuminates cooking area",
                    "Easy-to-clean filters for hassle-free maintenance"
                ])
                if 'smart' in name_lower:
                    features.extend([
                        "Smart touch controls with intuitive interface",
                        "Auto-clean function reduces maintenance requirements"
                    ])

            # Add category-specific features if no product-specific ones found
            if not features and category:
                category_features = {
                    "Study Lamps": [
                        "Adjustable brightness levels for different tasks",
                        "Eye-care technology reduces strain",
                        "Flexible arm design for precise positioning"
                    ],
                    "Furniture fittings": [
                        "Precision engineering ensures perfect fit",
                        "Corrosion-resistant coating for longevity",
                        "Professional-grade quality and reliability"
                    ],
                    "Smart Home Accessories": [
                        "Advanced smart connectivity features",
                        "Mobile app control for convenience",
                        "Energy-efficient operation saves costs"
                    ],
                    "Electronics": [
                        "Cutting-edge technology for superior performance",
                        "User-friendly interface and controls",
                        "Reliable operation with quality components"
                    ],
                    "Kitchen Appliance": [
                        "Energy-efficient design reduces power consumption",
                        "Easy operation with intuitive controls",
                        "Durable construction for years of reliable use"
                    ],
                    "Hood": [
                        "Powerful suction removes smoke and cooking odors",
                        "Quiet operation maintains peaceful kitchen environment",
                        "Stylish design complements modern kitchen decor"
                    ]
                }
                features = category_features.get(category, [
                    "High-quality construction ensures durability",
                    "Reliable performance for consistent results",
                    "User-friendly design for easy operation"
                ])

            return features[:6]  # Limit to 6 features for optimal display

        except Exception as e:
            logger.error(f"Error generating features: {str(e)}")
            return ["High-quality construction", "Reliable performance", "User-friendly design"]

    def generate_technical_specifications(self, product_name: str, brand: str = None, category: str = None) -> List[str]:
        """Generate technical specifications from product name analysis"""
        try:
            name_lower = product_name.lower()
            specifications = []

            # Extract specifications from product name
            import re

            # Look for dimensions
            dimension_pattern = r'(\d+)\s*(cm|mm|inch|inches|")'
            dimensions = re.findall(dimension_pattern, name_lower)
            if dimensions:
                for dim, unit in dimensions:
                    specifications.append(f"Dimension: {dim} {unit}")

            # Look for resolution
            resolution_patterns = [r'(\d+k)', r'(\d+p)', r'(\d+x\d+)']
            for pattern in resolution_patterns:
                matches = re.findall(pattern, name_lower)
                if matches:
                    specifications.append(f"Resolution: {matches[0].upper()}")

            # Look for pressure ratings
            pressure_pattern = r'(\d+)\s*psi'
            pressure = re.findall(pressure_pattern, name_lower)
            if pressure:
                specifications.append(f"Maximum Pressure: {pressure[0]} PSI")

            # Look for power ratings
            power_pattern = r'(\d+)\s*(w|watt|watts)'
            power = re.findall(power_pattern, name_lower)
            if power:
                specifications.append(f"Power Consumption: {power[0][0]} {power[0][1].upper()}")

            # Look for model numbers
            model_pattern = r'([A-Z]{2,}\d+[A-Z]*\d*)'
            models = re.findall(model_pattern, product_name)
            if models:
                specifications.append(f"Model Number: {models[0]}")

            # Look for colors
            colors = ['white', 'black', 'silver', 'grey', 'blue', 'red', 'green']
            for color in colors:
                if color in name_lower:
                    specifications.append(f"Color: {color.title()}")
                    break

            # Add brand if available
            if brand:
                specifications.append(f"Brand: {brand}")

            # Add category-specific specifications
            if category and not specifications:
                category_specs = {
                    "Study Lamps": ["Type: LED Desk Lamp", "Power Source: AC Adapter", "Adjustability: Multi-angle"],
                    "Furniture fittings": ["Material: Premium Metal", "Finish: Corrosion Resistant", "Installation: Easy Mount"],
                    "Electronics": ["Technology: Advanced Digital", "Interface: User Friendly", "Warranty: Manufacturer Backed"],
                    "Kitchen Appliance": ["Type: Kitchen Equipment", "Energy Rating: Efficient", "Maintenance: Easy Clean"],
                    "Hood": ["Type: Kitchen Ventilation", "Motor: High Performance", "Control: Multi Speed"]
                }
                specifications = category_specs.get(category, ["Type: Quality Product", "Material: Premium Grade"])

            return specifications[:5]  # Limit to 5 specifications

        except Exception as e:
            logger.error(f"Error generating specifications: {str(e)}")
            return ["Material: High Quality", "Design: Professional Grade"]

    def generate_product_benefits(self, product_name: str, brand: str = None, category: str = None) -> List[str]:
        """Generate product benefits based on analysis"""
        try:
            name_lower = product_name.lower()
            benefits = []

            # Product-specific benefits
            if any(word in name_lower for word in ['lamp', 'light', 'led']):
                benefits.extend([
                    "Reduces eye strain during long study or work sessions",
                    "Energy-efficient operation significantly reduces electricity costs",
                    "Long-lasting LED technology provides years of reliable illumination"
                ])

            elif any(word in name_lower for word in ['hinge', 'clip', 'soft close']):
                benefits.extend([
                    "Smooth and quiet operation enhances user experience",
                    "Extends furniture lifespan with gentle closing mechanism",
                    "Professional installation quality without expert help required"
                ])

            elif any(word in name_lower for word in ['camera', 'dashcam']):
                benefits.extend([
                    "Provides crucial evidence in case of accidents or incidents",
                    "Continuous monitoring ensures vehicle security when parked",
                    "High-quality footage supports insurance claims and legal proceedings"
                ])

            elif any(word in name_lower for word in ['door bell', 'doorbell']):
                benefits.extend([
                    "Enhanced home security with real-time visitor monitoring",
                    "Never miss important deliveries with instant mobile notifications",
                    "Two-way communication allows safe interaction with visitors"
                ])

            elif any(word in name_lower for word in ['pump', 'air pump']):
                benefits.extend([
                    "Maintains optimal tire pressure for improved safety and fuel efficiency",
                    "Portable design ensures emergency inflation capability anywhere",
                    "Saves time and money by eliminating gas station visits"
                ])

            elif any(word in name_lower for word in ['hood', 'kitchen']):
                benefits.extend([
                    "Maintains clean and fresh kitchen environment during cooking",
                    "Effectively removes cooking odors and smoke for comfortable living",
                    "Protects kitchen surfaces and cabinets from grease buildup"
                ])

            # Add brand-specific benefits
            if brand:
                brand_benefits = {
                    "qubo": "Backed by Qubo's innovative smart home technology and reliability",
                    "haier": "Supported by Haier's global expertise in home appliances",
                    "godrej": "Trusted Godrej quality ensures long-term value and satisfaction",
                    "bosch": "German engineering excellence provides superior performance",
                    "lg": "Advanced LG technology delivers innovative features and efficiency"
                }
                brand_benefit = brand_benefits.get(brand.lower(), f"Backed by {brand}'s commitment to quality and customer satisfaction")
                benefits.append(brand_benefit)

            # Add general benefits if none found
            if not benefits:
                benefits = [
                    "Designed for long-lasting performance and reliability",
                    "Easy to use and maintain with user-friendly design",
                    "Excellent value for money with premium quality features"
                ]

            return benefits[:5]  # Limit to 5 benefits

        except Exception as e:
            logger.error(f"Error generating benefits: {str(e)}")
            return ["High-quality construction ensures durability", "Reliable performance for consistent results"]

    def generate_ideal_for_section(self, product_name: str, category: str = None) -> List[str]:
        """Generate ideal for/usage recommendations"""
        try:
            name_lower = product_name.lower()

            if any(word in name_lower for word in ['lamp', 'light', 'study']):
                return [
                    "Study rooms and home offices for focused work",
                    "Reading corners and workspaces requiring task lighting",
                    "Student dormitories and libraries for extended study sessions"
                ]

            elif any(word in name_lower for word in ['hinge', 'furniture']):
                return [
                    "Kitchen cabinets and wardrobes requiring smooth operation",
                    "Furniture manufacturing and repair projects",
                    "Home improvement and renovation applications"
                ]

            elif any(word in name_lower for word in ['camera', 'dashcam']):
                return [
                    "Personal and commercial vehicles for security monitoring",
                    "Fleet management and driver behavior monitoring",
                    "Insurance documentation and accident evidence recording"
                ]

            elif any(word in name_lower for word in ['door bell', 'doorbell']):
                return [
                    "Residential homes and apartments for enhanced security",
                    "Small offices and businesses monitoring visitor access",
                    "Rental properties and vacation homes for remote monitoring"
                ]

            elif any(word in name_lower for word in ['pump', 'air pump']):
                return [
                    "Car and motorcycle maintenance for optimal tire pressure",
                    "Emergency roadside assistance and breakdown situations",
                    "Bicycle and sports equipment inflation needs"
                ]

            elif any(word in name_lower for word in ['hood', 'kitchen']):
                return [
                    "Modern kitchens and cooking spaces requiring ventilation",
                    "Restaurants and commercial kitchens with heavy cooking",
                    "Home renovation projects upgrading kitchen facilities"
                ]

            else:
                if category:
                    return [
                        f"Professional {category.lower()} applications and projects",
                        "Home and personal use requiring quality performance",
                        "Commercial and industrial settings demanding reliability"
                    ]
                else:
                    return [
                        "Professional and personal use applications",
                        "Home and office environments requiring quality solutions",
                        "Various commercial settings demanding reliable performance"
                    ]

        except Exception as e:
            logger.error(f"Error generating ideal for section: {str(e)}")
            return ["Professional and personal use", "Home and office applications"]

    def compile_structured_description(self, overview: str, features: List[str],
                                     specifications: List[str], benefits: List[str],
                                     ideal_for: List[str]) -> str:
        """Compile all sections into structured description format"""
        try:
            description_parts = []

            # Add overview
            if overview:
                description_parts.append(overview)

            # Add Key Features section
            if features:
                description_parts.append("")  # Add spacing
                description_parts.append("Key Features:")
                for feature in features:
                    description_parts.append(f"• {feature}")

            # Add Technical Specifications section
            if specifications:
                description_parts.append("")  # Add spacing
                description_parts.append("Technical Specifications:")
                for spec in specifications:
                    description_parts.append(f"• {spec}")

            # Add Benefits section
            if benefits:
                description_parts.append("")  # Add spacing
                description_parts.append("Benefits:")
                for benefit in benefits:
                    description_parts.append(f"• {benefit}")

            # Add Ideal For section
            if ideal_for:
                description_parts.append("")  # Add spacing
                description_parts.append("Ideal For:")
                for usage in ideal_for:
                    description_parts.append(f"• {usage}")

            return "\n".join(description_parts)

        except Exception as e:
            logger.error(f"Error compiling structured description: {str(e)}")
            return overview or "High-quality product designed for optimal performance."

    def generate_fallback_description(self, product_name: str, brand: str = None, category: str = None) -> str:
        """Generate fallback description when other methods fail"""
        try:
            fallback_parts = []

            if brand:
                fallback_parts.append(f"The {brand} {product_name} is a premium quality product")
            else:
                fallback_parts.append(f"The {product_name} is a high-quality product")

            if category:
                fallback_parts.append(f"designed for {category.lower()} applications.")
            else:
                fallback_parts.append("engineered for optimal performance and reliability.")

            fallback_parts.append("\n\nKey Features:")
            fallback_parts.append("• High-quality construction ensures long-lasting durability")
            fallback_parts.append("• Professional-grade performance for consistent results")
            fallback_parts.append("• User-friendly design for easy operation and maintenance")

            fallback_parts.append("\n\nBenefits:")
            fallback_parts.append("• Reliable performance you can depend on")
            fallback_parts.append("• Excellent value for money with premium quality")
            fallback_parts.append("• Suitable for both professional and personal use")

            return " ".join(fallback_parts)

        except Exception as e:
            logger.error(f"Error generating fallback description: {str(e)}")
            return f"High-quality {product_name} designed for optimal performance and reliability."

    def clean_description_text(self, text: str) -> str:
        """Clean and format description text"""
        try:
            # Remove HTML tags
            text = re.sub(r'<[^>]+>', '', text)
            # Remove extra whitespace
            text = re.sub(r'\s+', ' ', text)
            # Remove special characters but keep basic punctuation
            text = re.sub(r'[^\w\s\-\.,;:()%]', '', text)
            # Capitalize first letter
            text = text.strip()
            if text:
                text = text[0].upper() + text[1:]
            return text
        except Exception:
            return text

    def is_generic_content(self, text: str) -> bool:
        """Check if content is too generic"""
        try:
            if not text or len(text) < 30:
                return True

            generic_phrases = [
                'high quality', 'reliable performance', 'durable construction',
                'professional-grade performance and reliability',
                'designed for optimal performance', 'excellent features and performance'
            ]

            text_lower = text.lower()
            return any(phrase in text_lower for phrase in generic_phrases)

        except Exception:
            return True

class EnhancedProductDataExtractor:
    """Handles enhanced product data extraction and processing"""

    def __init__(self):
        self.description_generator = StructuredDescriptionGenerator()

    def calculate_description_quality_score(self, description: str) -> int:
        """Calculate quality score for existing description (1-10)"""
        try:
            if not description:
                return 1

            desc_len = len(description.strip())

            # Length-based scoring
            if desc_len < 50:
                score = 2
            elif desc_len < 100:
                score = 4
            elif desc_len < 200:
                score = 6
            elif desc_len < 400:
                score = 8
            else:
                score = 9

            # Check for structured content
            if any(keyword in description.lower() for keyword in ['features:', 'specifications:', 'benefits:']):
                score += 1

            # Check for generic content (reduce score)
            if self.description_generator.is_generic_content(description):
                score = max(1, score - 2)

            return min(10, max(1, score))

        except Exception:
            return 1

    def get_products_needing_enhancement(self, min_quality_score: int = 6) -> List[ProductData]:
        """Get products that need enhanced descriptions"""
        try:
            logger.info(f"Retrieving products with quality score below {min_quality_score}...")

            # Get all active products
            all_products = Product.objects.select_related(
                'category', 'subcategory', 'brand', 'gst'
            ).prefetch_related('images').filter(is_active=True)

            products_needing_enhancement = []
            total_products = all_products.count()

            logger.info(f"Analyzing {total_products} products for description quality...")

            for index, product in enumerate(all_products, 1):
                try:
                    # Calculate quality score
                    quality_score = self.calculate_description_quality_score(product.description)

                    # Only process products below quality threshold
                    if quality_score < min_quality_score:
                        logger.info(f"Processing product {index}/{total_products}: {product.name} (Quality: {quality_score}/10)")

                        # Get product images
                        images = []
                        for img in product.images.all():
                            if img.image:
                                images.append(img.image.name)

                        # Generate enhanced description
                        enhanced_description = self.description_generator.generate_structured_description(
                            product_name=product.name,
                            brand=product.brand.name if product.brand else None,
                            category=product.category.name if product.category else None,
                            existing_description=product.description
                        )

                        # Create product data object
                        product_data = ProductData(
                            id=product.id,
                            name=product.name,
                            slug=product.slug,
                            original_description=product.description or "",
                            enhanced_description=enhanced_description,
                            category=product.category.name if product.category else None,
                            subcategory=product.subcategory.name if product.subcategory else None,
                            brand=product.brand.name if product.brand else None,
                            price=float(product.price),
                            mrp=float(product.mrp),
                            base_price=float(product.base_price),
                            gst_rate=float(product.get_gst_rate().rate),
                            gst_amount=float(product.calculate_gst_from_mrp()),
                            stock=product.stock,
                            is_active=product.is_active,
                            created_at=product.created_at.isoformat(),
                            updated_at=product.updated_at.isoformat(),
                            images=images,
                            description_quality_score=quality_score
                        )

                        products_needing_enhancement.append(product_data)

                        # Add delay every 10 products
                        if index % 10 == 0:
                            logger.info(f"Processed {index} products, taking a short break...")
                            time.sleep(2)
                    else:
                        logger.debug(f"Skipping product {index}/{total_products}: {product.name} (Quality: {quality_score}/10 - Good)")

                except Exception as e:
                    logger.error(f"Error processing product {product.name}: {str(e)}")
                    continue

            logger.info(f"Found {len(products_needing_enhancement)} products needing enhancement out of {total_products} total")
            return products_needing_enhancement

        except Exception as e:
            logger.error(f"Error retrieving products: {str(e)}")
            raise

    def save_enhanced_products_to_json(self, products: List[ProductData], output_dir: str) -> str:
        """Save enhanced products to JSON file"""
        try:
            # Ensure output directory exists
            os.makedirs(output_dir, exist_ok=True)

            # Generate filename with timestamp
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"enhanced_descriptions_{timestamp}.json"
            filepath = os.path.join(output_dir, filename)

            # Convert products to dictionaries
            products_dict = {
                "metadata": {
                    "generated_at": datetime.now().isoformat(),
                    "total_products": len(products),
                    "script_version": "2.0_enhanced",
                    "description_format": "structured_for_frontend_component",
                    "quality_threshold": "products_with_quality_score_below_6"
                },
                "products": [product.to_dict() for product in products]
            }

            # Save to JSON file
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(products_dict, f, indent=2, ensure_ascii=False, cls=DjangoJSONEncoder)

            logger.info(f"Successfully saved {len(products)} enhanced products to {filepath}")
            return filepath

        except Exception as e:
            logger.error(f"Error saving enhanced products to JSON: {str(e)}")
            raise

def main():
    """Main function to run the enhanced description generation"""
    try:
        logger.info("Starting Enhanced Product Description Generation Script...")
        logger.info("This script generates structured descriptions optimized for the frontend component")

        # Initialize extractor
        extractor = EnhancedProductDataExtractor()

        # Ask user for quality threshold
        print("\nDescription Quality Scoring:")
        print("1-2: No description or very poor")
        print("3-4: Short, basic description")
        print("5-6: Adequate description")
        print("7-8: Good, detailed description")
        print("9-10: Excellent, comprehensive description")

        try:
            threshold_input = input("\nEnter minimum quality score for enhancement (default: 6): ").strip()
            min_quality_score = int(threshold_input) if threshold_input else 6
            min_quality_score = max(1, min(10, min_quality_score))  # Ensure valid range
        except ValueError:
            min_quality_score = 6
            print("Invalid input, using default threshold of 6")

        print(f"\nProcessing products with quality score below {min_quality_score}...")

        # Get products that need enhanced descriptions
        products = extractor.get_products_needing_enhancement(min_quality_score)

        if not products:
            logger.warning(f"No products found with quality score below {min_quality_score}")
            print(f"\nNo products need enhancement with the current threshold of {min_quality_score}")
            print("All products already have good descriptions!")
            return

        # Show summary before saving
        print(f"\nFound {len(products)} products needing enhancement:")

        # Group by quality score for summary
        quality_summary = {}
        for product in products:
            score = product.description_quality_score
            if score not in quality_summary:
                quality_summary[score] = 0
            quality_summary[score] += 1

        for score in sorted(quality_summary.keys()):
            print(f"  Quality {score}/10: {quality_summary[score]} products")

        # Ask for confirmation
        response = input(f"\nProceed to save enhanced descriptions for {len(products)} products? (y/N): ").strip().lower()
        if response != 'y':
            print("Operation cancelled by user")
            return

        # Save to JSON file
        output_dir = os.path.join(os.path.dirname(__file__), "data new qubo jsons")
        filepath = extractor.save_enhanced_products_to_json(products, output_dir)

        logger.info("Enhanced description generation completed successfully!")

        # Print summary
        print("\n" + "="*70)
        print("ENHANCED PRODUCT DESCRIPTION GENERATION SUMMARY")
        print("="*70)
        print(f"Quality threshold used: {min_quality_score}/10")
        print(f"Total products processed: {len(products)}")
        print(f"Output file: {filepath}")
        print(f"Generated at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("\nQuality Score Distribution:")
        for score in sorted(quality_summary.keys()):
            print(f"  Score {score}/10: {quality_summary[score]} products")
        print("\nNext Steps:")
        print("1. Review the generated descriptions in the JSON file")
        print("2. Use 'update_existing_descriptions.py' to apply changes to database")
        print("3. Test the descriptions on your frontend component")
        print("="*70)

    except Exception as e:
        logger.error(f"Script execution failed: {str(e)}")
        raise

if __name__ == "__main__":
    main()
